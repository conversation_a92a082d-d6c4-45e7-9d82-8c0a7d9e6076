import { Suspense } from 'react';
import { BiLinkExternal } from 'react-icons/bi';
import type {
  StatsAPI,
  TotalPriceLatestAPI,
} from '@repo/types/website-api-types';
import { Link, Skeleton, SkeletonTable, Text } from '@repo/ui/components';
import { fetchTotalPriceLatest } from '@/api-proxy/dtao';
import { fetchStats } from '@/api-proxy/stats';
import { Bittensor } from '@/components/icons/bittensor';
import { SubnetSwitch } from '@/components/views/dtao';
import { SubnetsTableDynamic } from '@/components/views/dtao/subnets-table.dynamic';
import { RegistrationCostChartDynamic } from '@/components/views/subnets/registration-cost-chart.dynamic';
import { SubnetTotalPriceChartDynamic } from '@/components/views/subnets/subnet-total-price-chart.dynamic';
import { TopEmissionChartDynamic } from '@/components/views/subnets/top-emission-chart.dynamic';
import { constructMetadata } from '@/lib/meta';

export const metadata = constructMetadata({
  title: 'Subnets · taostats · Bittensor',
  description:
    'Bittensor subnets, emissions, ownership, registration date, immunity and recycle volume data.',
});

export default function SubnetPage() {
  return (
    <div className='relative flex flex-col'>
      <div className='flex flex-col gap-6 px-4 pb-6 md:gap-6 md:px-10 2xl:px-24'>
        <div className='flex flex-col gap-12'>
          <div className='flex flex-col items-start justify-between gap-4 pt-8 sm:flex-row sm:items-center'>
            <div className='flex flex-row gap-3'>
              <Text level='xxlTitle' className='font-medium'>
                Subnets
              </Text>
              <Link
                href='https://docs.taostats.io/docs/subnets'
                className='flex w-max items-center gap-2 hover:underline'
                target='_blank'
              >
                <BiLinkExternal size={24} color='#00DBBC' />
              </Link>
            </div>
            <SubnetSwitch />
          </div>

          <Suspense
            fallback={
              <div className='flex flex-col gap-12'>
                <div className='grid grid-cols-12 gap-6'>
                  {Array.from({ length: 4 }).map((_, index) => (
                    <Skeleton
                      className='col-span-12 h-48 w-full sm:col-span-6 2xl:col-span-3'
                      key={index}
                    />
                  ))}
                </div>
                <SkeletonTable padding={false} />
              </div>
            }
          >
            <SubnetTable />
          </Suspense>
        </div>

        <SubnetTotalPriceChart />

        <TopEmissionChart />

        <RegistrationCostChart />
      </div>
    </div>
  );
}

const TopEmissionChart = () => {
  return (
    <div
      className='flex flex-col gap-4 rounded-2xl border border-neutral-800 bg-neutral-900 p-4 shadow-lg md:p-8'
      id='top-emission-chart'
    >
      <div className='flex flex-col gap-2'>
        <Text
          level='mdTitle'
          className='self-start whitespace-nowrap font-medium'
        >
          Subnet <span className='font-thin'>Top Emissions</span>
        </Text>
        <Text level='sm' className='max-w-lg opacity-50'>
          The price of the subnet divided by the sum of the prices of all active
          subnets splits the emission amongst the subnets.
        </Text>
      </div>

      <TopEmissionChartDynamic />
    </div>
  );
};

const RegistrationCostChart = () => {
  return (
    <div
      className='flex flex-col gap-2 rounded-2xl border border-neutral-800 bg-neutral-900 p-4 shadow-lg md:p-8'
      id='subnet-registration-chart'
    >
      <Text level='mdTitle' className='flex items-center gap-2 font-medium'>
        Subnet<span className='font-thin'>Registration Cost</span> <Bittensor />
      </Text>

      <RegistrationCostChartDynamic />
    </div>
  );
};

const SubnetTable = async () => {
  const response = await Promise.allSettled([
    fetchTotalPriceLatest(),
    fetchStats(),
  ]);

  const sentimentData =
    response[0].status === 'fulfilled'
      ? (response[0].value as TotalPriceLatestAPI).data
      : [];

  const stake =
    response[1].status === 'fulfilled'
      ? (response[1].value as StatsAPI | null)?.data[0] ?? null
      : null;

  return <SubnetsTableDynamic sentimentData={sentimentData} stake={stake} />;
};

const SubnetTotalPriceChart = () => {
  return (
    <div className='flex flex-col gap-2 rounded-2xl border border-neutral-800 bg-neutral-900 p-4 shadow-lg md:p-8'>
      <div className='flex flex-col gap-2'>
        <Text
          level='mdTitle'
          className='self-start whitespace-nowrap font-medium'
        >
          Subnets <span className='font-thin'>Total Price</span>
        </Text>
        <Text level='sm' className='max-w-lg opacity-50'>
          The sum of all subnet prices in Tao
        </Text>
      </div>
      <SubnetTotalPriceChartDynamic />
    </div>
  );
};
