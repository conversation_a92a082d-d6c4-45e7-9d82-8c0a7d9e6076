import { Hono } from 'hono';
import {
  fetchAllPoolTotalPriceHistory,
  fetchColdkeyAlphaShares,
  fetchDtaoSubnet,
  fetchHeatmapHotkeyNetuid,
  fetchHotkeyAlphaShares,
  fetchHotkeyEmission,
  fetchPool,
  fetchStakeBalance,
  fetchStakeBalanceAggregated,
  fetchSubnetEmission,
  fetchSubnetPrice,
  fetchTaoStakedAlpha,
  fetchTaoStakedRoot,
  fetchValidatorYieldLatest,
} from '@/api-proxy/dtao';

const app = new Hono()
  .get('/pool', async (c) => {
    const response = await fetchPool({ ...c.req.query() });
    return c.json(response);
  })
  .get('/dtaoSubnetEmission', async (c) => {
    const response = await fetchSubnetEmission({ ...c.req.query() });
    return c.json(response);
  })
  .get('/hotkeyEmission', async (c) => {
    const response = await fetchHotkeyEmission({ ...c.req.query() });
    return c.json(response);
  })
  .get('/hotkeyAlphaShares', async (c) => {
    const response = await fetchHotkeyAlphaShares({ ...c.req.query() });
    return c.json(response);
  })
  .get('/coldkeyAlphaShares', async (c) => {
    const response = await fetchColdkeyAlphaShares({ ...c.req.query() });
    return c.json(response);
  })
  .get('/stakeBalance', async (c) => {
    const response = await fetchStakeBalance({ ...c.req.query() });
    return c.json(response);
  })
  .get('/stakeBalanceAggregated', async (c) => {
    const response = await fetchStakeBalanceAggregated({ ...c.req.query() });
    return c.json(response);
  })
  .get('/dtaoSubnets', async (c) => {
    const response = await fetchDtaoSubnet({ ...c.req.query() });
    return c.json(response);
  })
  .get('/validatorYieldLatest', async (c) => {
    const response = await fetchValidatorYieldLatest({ ...c.req.query() });
    return c.json(response);
  })
  .get('/heatmapHotkeyNetuid', async (c) => {
    const response = await fetchHeatmapHotkeyNetuid();
    return c.json(response);
  })
  .get('/taoStakedRoot', async (c) => {
    const response = await fetchTaoStakedRoot({ ...c.req.query() });
    return c.json(response);
  })
  .get('/taoStakedAlpha', async (c) => {
    const response = await fetchTaoStakedAlpha({ ...c.req.query() });
    return c.json(response);
  })
  .get('/subnetPrice', async (c) => {
    const id = Number(c.req.query().id);
    const type = c.req.query().type;
    const timestampStart = c.req.query().timestampStart;

    const response = await fetchSubnetPrice({
      id,
      type,
      timestamp_start: timestampStart,
    });
    return c.json(response);
  })
  .get('/subnetTotalPrice', async (c) => {
    const response = await fetchAllPoolTotalPriceHistory();
    return c.json(response);
  });

export const dtaoApi = app;
