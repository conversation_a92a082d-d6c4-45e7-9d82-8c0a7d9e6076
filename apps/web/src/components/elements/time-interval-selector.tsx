import { useCallback, useMemo, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components';
import { cn } from '@repo/ui/lib';

export const TimeIntervalSelector = ({
  defaultSelect = '7D',
  placeholder,
  onIntervalChange,
  variant = 'TIME',
  className,
}: {
  defaultSelect?: string;
  placeholder?: string;
  onIntervalChange?: (value: string) => void;
  variant?: 'TIME' | 'DAY' | 'PERFORMANCE';
  className?: string;
}) => {
  const [selectValue, setSelectValue] = useState<string>(defaultSelect);

  const selectOptions = useMemo(
    () => [
      {
        label: '24H',
        value: '24H',
      },
      {
        label: '7D',
        value: '7D',
      },
      {
        label: '30D',
        value: '30D',
      },
      {
        label: 'ALL',
        value: 'ALL',
      },
    ],
    []
  );

  const finalOptions = useMemo(
    () =>
      variant === 'TIME'
        ? selectOptions
        : variant === 'DAY'
          ? selectOptions.slice(1)
          : selectOptions.slice(0, 2),
    [selectOptions, variant]
  );

  const handleValueChange = useCallback(
    (value: string) => {
      setSelectValue(value);
      if (onIntervalChange) {
        onIntervalChange(value);
      }
    },
    [onIntervalChange]
  );

  return (
    <Select value={selectValue} onValueChange={handleValueChange}>
      <SelectTrigger
        className={cn(
          'h-9 w-24 text-xs !outline-none !ring-0',
          className,
          selectValue && selectValue.length > 0
            ? 'text-white'
            : 'text-neutral-500'
        )}
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent className='border-[#404040] bg-[#141414] text-sm'>
        <SelectGroup>
          {finalOptions.map(({ label, value }) => (
            <SelectItem value={value} key={label}>
              {label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};
