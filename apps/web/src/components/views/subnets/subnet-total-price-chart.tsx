'use client';

import { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { localPoint } from '@visx/event';
import { LinearGradient } from '@visx/gradient';
import { Group } from '@visx/group';
import { ParentSize } from '@visx/responsive';
import {
  AreaSeries,
  Axis,
  BarSeries,
  Grid,
  Tooltip,
  type TooltipData,
  XYChart,
  buildChartTheme,
} from '@visx/xychart';
import { Zoom } from '@visx/zoom';
import type { ProvidedZoom, TransformMatrix } from '@visx/zoom/lib/types';
import * as d3 from 'd3';
import { utc } from 'moment';
import { format } from 'numerable';
import { BiMinus, BiPlus } from 'react-icons/bi';
import { Button, Separator, Skeleton, Text } from '@repo/ui/components';
import { useWindowSize } from '@repo/ui/lib';
import { Bittensor } from '@/components/icons/bittensor';
import { useSubnetTotalPrice } from '@/lib/hooks';
import { useSubnetSwitch } from '@/lib/hooks/use-subnet-switch';
import { useLatestPriceAtom } from '@/store/use-latest-price';

const accessors = {
  xAccessor: (d: ChartPoint) => d.x,
  yAccessor: (d: ChartPoint) => d.y,
};

type ChartPoint = {
  x: Date;
  y: number;
  oldY: number;
  buy: number;
  sell: number;
};

export const Chart = memo(({ width }: { width: number }) => {
  const height = 400;
  const maxScale = 8;

  const { subnetSwitch } = useSubnetSwitch();
  const { latestPrice: taoValue } = useLatestPriceAtom();
  const { isMobile } = useWindowSize();
  const { data: subnetTotal, isPending } = useSubnetTotalPrice();

  const zoomRef = useRef<
    | (ProvidedZoom<SVGSVGElement> & {
        initialTransformMatrix: TransformMatrix;
        transformMatrix: TransformMatrix;
        isDragging: boolean;
      })
    | null
  >();

  const initialTransform = {
    scaleX: 1,
    scaleY: 1,
    translateX: 0,
    translateY: 0,
    skewX: 0,
    skewY: 0,
  };

  const chartData = useMemo(() => subnetTotal ?? [], [subnetTotal]);

  const [volumeMin, volumeMax] = useMemo(() => {
    const min = 0;
    const max = Math.max(
      ...chartData.map(
        (item) =>
          (subnetSwitch === 'TAO'
            ? Number(item.alpha_volume)
            : Number(item.alpha_volume) * taoValue) / 1e9
      )
    );

    return [min, max];
  }, [chartData, subnetSwitch, taoValue]);

  const [priceMin, priceMax] = useMemo(() => {
    const min = 0;

    const max = Math.max(
      ...chartData.map((item) =>
        subnetSwitch === 'TAO'
          ? Number(item.price)
          : Number(item.price) * taoValue
      )
    );
    return [min, max];
  }, [chartData, subnetSwitch, taoValue]);

  const convertUpdateTickToEmissionScale = useCallback(
    (tick: number) => {
      const volumeDomain = volumeMax - volumeMin;
      const priceDomain = priceMax - priceMin;

      const convertedTick =
        Math.round(
          (((tick - priceMin) / priceDomain) * volumeDomain + volumeMin) / 50000
        ) * 50000;

      return Math.max(0, convertedTick).toFixed(0);
    },
    [volumeMin, volumeMax, priceMin, priceMax]
  );

  const data = useMemo<Date[]>(() => {
    return chartData.map((item) => new Date(new Date(item.timestamp)));
  }, [chartData]);

  const xDomain = useMemo<[undefined, undefined] | [Date, Date]>(() => {
    return d3.extent(data, (d) => d);
  }, [data]);

  const dataMap = useMemo<
    Map<string, { price: number; volume: number; buy: number; sell: number }>
  >(() => {
    const map = new Map<
      string,
      { price: number; volume: number; buy: number; sell: number }
    >();

    chartData.forEach((item) => {
      const price =
        subnetSwitch === 'TAO'
          ? Number(item.price)
          : Number(item.price) * taoValue;
      const volume =
        subnetSwitch === 'TAO'
          ? Number(item.alpha_volume) / 1e9
          : (Number(item.alpha_volume) * taoValue) / 1e9;
      const buy =
        subnetSwitch === 'TAO'
          ? Number(item.alpha_buy_volume) / 1e9
          : (Number(item.alpha_buy_volume) * taoValue) / 1e9;
      const sell =
        subnetSwitch === 'TAO'
          ? Number(item.alpha_sell_volume) / 1e9
          : (Number(item.alpha_sell_volume) * taoValue) / 1e9;

      map.set(new Date(new Date(item.timestamp)).toISOString(), {
        price: Number(price.toFixed(3)),
        volume: Number(volume.toFixed(3)),
        buy: Number(buy.toFixed(3)),
        sell: Number(sell.toFixed(3)),
      });
    });

    return map;
  }, [chartData, subnetSwitch, taoValue]);

  const customTheme = useMemo(
    () =>
      buildChartTheme({
        backgroundColor: '',
        colors: ['#00DBBC', '#00DBBC', '#EB5347'],
        gridColor: '#e8e8e8',
        gridColorDark: '#222831',
        tickLength: 1,
      }),
    []
  );

  const handleRenderTooltip = useCallback(
    ({ tooltipData }: { tooltipData?: TooltipData }) => {
      if (!tooltipData || !tooltipData.nearestDatum) {
        return null;
      }

      const { key } = tooltipData.nearestDatum;
      const datum1 = tooltipData.datumByKey['Line 1'].datum as ChartPoint;

      return (
        <div
          key={key}
          className='z-10 flex flex-col gap-2 rounded-lg border border-[#323232] bg-[#1D1D1D]/60 p-3 backdrop-blur-xl'
        >
          <Text level='sm' className='font-medium opacity-70'>
            {utc(datum1.x).format('DD MMM YYYY')}
          </Text>
          <Separator />
          <div className='flex flex-row items-center gap-1'>
            <Text level='sm' className='font-medium opacity-70'>
              Total Price:
            </Text>
            <Text
              level='sm'
              className='flex flex-row font-medium text-[#FFFFFF]'
            >
              {subnetSwitch === 'TAO' ? (
                <Bittensor className='-mb-0.5 -ml-2 -mr-1 text-[#FFFFFF]' />
              ) : (
                '$'
              )}
              {format(datum1.oldY, '0.000a')}
            </Text>
          </div>
          <div className='flex flex-row items-center gap-1'>
            <Text level='sm' className='font-medium opacity-70'>
              Total Volume:
            </Text>
            <Text
              level='sm'
              className='flex flex-row items-center font-medium text-[#FFFFFF]'
            >
              {subnetSwitch === 'TAO' ? (
                <Bittensor className='-mb-0.5 -ml-2 -mr-1 text-[#FFFFFF]' />
              ) : (
                '$'
              )}
              {format(datum1.buy + datum1.sell, '0.00a')}
            </Text>
          </div>
          <div className='flex flex-row items-center gap-1'>
            <Text level='sm' className='font-medium opacity-70'>
              Buy Total:
            </Text>
            <Text
              level='sm'
              className='flex flex-row items-center font-medium text-[#00DBBC]'
            >
              {subnetSwitch === 'TAO' ? (
                <Bittensor className='-mb-0.5 -ml-2 -mr-1 text-[#00DBBC]' />
              ) : (
                '$'
              )}
              {format(datum1.buy, '0.00a')}
            </Text>
          </div>
          <div className='flex flex-row items-center gap-1'>
            <Text level='sm' className='font-medium opacity-70'>
              Sell Total:
            </Text>
            <Text
              level='sm'
              className='flex flex-row items-center font-medium text-[#EB5347]'
            >
              {subnetSwitch === 'TAO' ? (
                <Bittensor className='-mb-0.5 -ml-2 -mr-1 text-[#EB5347]' />
              ) : (
                '$'
              )}
              {format(datum1.sell, '0.00a')}
            </Text>
          </div>
        </div>
      );
    },
    [subnetSwitch]
  );

  const getScaledData = useCallback(
    (
      transformMatrix: TransformMatrix
    ): { reScaledData1: ChartPoint[]; reScaledData2: ChartPoint[] } => {
      const price: ChartPoint[] = [];
      const volume: ChartPoint[] = [];

      if (xDomain[0] instanceof Date && xDomain[1] instanceof Date) {
        const scaleX = transformMatrix.scaleX;
        const xMovement = -transformMatrix.translateX;

        const paddingTime = 24 * 60 * 60 * 1000;

        const domainWidth =
          (xDomain[1].getTime() - xDomain[0].getTime() + paddingTime) / scaleX;

        const domainStart = new Date(
          xDomain[0].getTime() + (xMovement * domainWidth) / width
        );
        const domainEnd = new Date(domainStart.getTime() + domainWidth);

        for (const value of data) {
          const rawX = new Date(value);
          if (rawX >= domainStart && rawX <= domainEnd) {
            const x = rawX;
            const dateString = rawX.toISOString();
            const dataValue = dataMap.get(dateString);

            if (dataValue) {
              price.push({
                x,
                y: dataValue.price,
                oldY: dataValue.price,
                buy: dataValue.buy,
                sell: dataValue.sell,
              });
              volume.push({
                x,
                y: dataValue.volume,
                oldY: dataValue.volume,
                buy: dataValue.buy,
                sell: dataValue.sell,
              });
            }
          }
        }
      }

      return { reScaledData1: price, reScaledData2: volume };
    },
    [data, dataMap, width, xDomain]
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: intentional
  useEffect(() => {
    zoomRef.current?.reset();
  }, [width, isMobile]);

  const getRenderedScale = useCallback(
    (transformMatrix: TransformMatrix) => {
      const overFlowByScale = width * Math.max(0, transformMatrix.scaleX - 1);
      const adjustedMovement =
        transformMatrix.translateX > 0
          ? transformMatrix.translateX
          : Math.min(0, transformMatrix.translateX + overFlowByScale);
      return (
        (width / (width - Math.abs(adjustedMovement))) * transformMatrix.scaleX
      );
    },
    [width]
  );

  return (
    <>
      {isPending ? <Skeleton className='h-[450px]' /> : null}
      <div className={isPending ? 'hidden' : ''}>
        <Zoom<SVGSVGElement>
          width={width}
          height={height}
          scaleXMin={1}
          scaleXMax={maxScale}
          scaleYMin={1}
          scaleYMax={1}
          initialTransformMatrix={initialTransform}
          constrain={(transformMatrix, prevTransformMatrix) => {
            const scaleX = getRenderedScale(transformMatrix);

            if (transformMatrix.scaleX < 1) {
              return {
                ...prevTransformMatrix,
                translateX:
                  prevTransformMatrix.translateX * transformMatrix.scaleX,
              };
            }

            if (scaleX > maxScale || scaleX < 1) {
              return prevTransformMatrix;
            }

            return transformMatrix;
          }}
        >
          {(zoom) => {
            const { reScaledData1, reScaledData2 } = getScaledData(
              zoom.transformMatrix
            );

            if (!zoomRef.current) {
              zoomRef.current = zoom;
            }

            const transformMatrix = zoom.transformMatrix;

            const margin = { top: 20, right: 60, bottom: 60, left: 55 };

            // eslint-disable-next-line react-hooks/rules-of-hooks
            const stackedVolumeData = useMemo(() => {
              const volumeDomain = volumeMax - volumeMin;
              const updatedDomain = priceMax - priceMin;

              return reScaledData2.map((item) => {
                const totalVolume = item.buy + item.sell;
                if (totalVolume === 0) {
                  return {
                    x: item.x,
                    sellHeight: priceMin,
                    buyHeight: priceMin,
                    totalHeight: priceMin,
                    buy: item.buy,
                    sell: item.sell,
                  };
                }

                const totalVolumeScaled =
                  ((item.y - volumeMin) / volumeDomain) * updatedDomain +
                  priceMin;

                const sellRatio = item.sell / totalVolume;
                const buyRatio = item.buy / totalVolume;

                const buyHeight =
                  (totalVolumeScaled - priceMin) * buyRatio + priceMin;
                const sellHeight =
                  (totalVolumeScaled - priceMin) * sellRatio + buyHeight;

                return {
                  x: item.x,
                  buyHeight,
                  sellHeight,
                  totalHeight: totalVolumeScaled,
                  buy: item.buy,
                  sell: item.sell,
                };
              });
            }, [reScaledData2]);

            return (
              <div className='relative'>
                {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
                <svg
                  width={width}
                  height={height}
                  style={{
                    cursor: zoom.isDragging ? 'grabbing' : 'grab',
                    touchAction: 'none',
                  }}
                  ref={zoom.containerRef}
                >
                  <XYChart
                    height={height}
                    width={width}
                    xScale={{ type: 'band', paddingInner: 0.8 }}
                    yScale={{
                      type: 'linear',
                      domain: [priceMin, priceMax],
                      zero: true,
                    }}
                    margin={margin}
                    theme={customTheme}
                  >
                    <Grid
                      rows={false}
                      lineStyle={{
                        stroke: '#FFFFFF',
                        strokeOpacity: 0.2,
                        strokeWidth: 1,
                        strokeDasharray: '4 4',
                      }}
                    />
                    <Grid
                      columns={false}
                      lineStyle={{
                        stroke: '#FFFFFF',
                        strokeOpacity: 0.2,
                        strokeWidth: 1,
                        strokeDasharray: '0',
                      }}
                    />
                    <defs>
                      <LinearGradient
                        id='gradient1'
                        from='#00DBBC'
                        fromOpacity={0.2}
                        to='#00DBBC'
                        toOpacity={0}
                        x1='100%'
                        y1='0%'
                        x2='100%'
                        y2='100%'
                      />
                      <LinearGradient
                        id='buygradient'
                        from='#00DBBC'
                        fromOpacity={0.8}
                        to='#00DBBC'
                        toOpacity={0}
                        x1='100%'
                        y1='0%'
                        x2='100%'
                        y2='100%'
                      />
                      <LinearGradient
                        id='sellgradient'
                        from='#EB0000'
                        fromOpacity={0.9}
                        to='#EB0000'
                        toOpacity={0.1}
                        x1='100%'
                        y1='0%'
                        x2='100%'
                        y2='100%'
                      />
                    </defs>
                    <Group>
                      <AreaSeries
                        dataKey='Line 1'
                        data={reScaledData1.reverse().slice()}
                        lineProps={{
                          strokeWidth: 2,
                        }}
                        fill='url(#gradient1)'
                        {...accessors}
                      />
                      {/* Buy volume bars (bottom, teal) - offset from Y-axis */}
                      <BarSeries
                        dataKey='Buy Volume'
                        data={stackedVolumeData.reverse().slice()}
                        xAccessor={(d) => d.x}
                        yAccessor={(d) => d.buyHeight}
                        colorAccessor={() => 'url(#sellgradient)'}
                      />
                      {/* Sell volume bars (top, red) - stacked on top of buy */}
                      <BarSeries
                        dataKey='Sell Volume'
                        data={stackedVolumeData.reverse().slice()}
                        xAccessor={(d) => d.x}
                        yAccessor={(d) => d.sellHeight}
                        colorAccessor={() => 'url(#buygradient)'}
                      />
                    </Group>
                    <Group>
                      <Axis
                        hideAxisLine
                        tickLength={4}
                        tickStroke='#FFFFFF33'
                        label={
                          subnetSwitch === 'TAO' ? 'Price (τ)' : 'Price ($)'
                        }
                        orientation='left'
                        numTicks={8}
                        axisLineClassName='stroke-[#ffffff]/10'
                        tickFormat={(tick: number) => format(tick, '0.0a')}
                        labelProps={{
                          fill: '#FFFFFF',
                          fontSize: 12,
                          opacity: 0.4,
                          fontWeight: 500,
                          fontFamily: 'var(--font-sans)',
                          dx: '-25px',
                        }}
                        tickLabelProps={() => ({
                          fill: '#FFFFFF',
                          opacity: 0.4,
                          fontSize: 10,
                          fontWeight: 300,
                          fontFamily: 'var(--font-sans)',
                          dx: '-10px',
                        })}
                      />
                      <Axis
                        hideAxisLine
                        tickLength={4}
                        tickStroke='#FFFFFF33'
                        orientation='right'
                        label={
                          subnetSwitch === 'TAO' ? 'Volume (τ)' : 'Volume ($)'
                        }
                        numTicks={8}
                        axisLineClassName='stroke-[#ffffff]/10'
                        tickFormat={(value: number) =>
                          format(
                            convertUpdateTickToEmissionScale(value),
                            Number(convertUpdateTickToEmissionScale(value)) >
                              1000
                              ? '0a'
                              : ''
                          )
                        }
                        labelProps={{
                          fill: '#FFFFFF',
                          fontSize: 12,
                          opacity: 0.4,
                          fontWeight: 500,
                          fontFamily: 'var(--font-sans)',
                          dx: '32px',
                        }}
                        tickLabelProps={() => ({
                          fill: '#FFFFFF',
                          fontSize: 10,
                          opacity: 0.4,
                          fontWeight: 300,
                          fontFamily: 'var(--font-sans)',
                          dx: '10px',
                        })}
                      />
                      <Axis
                        hideAxisLine
                        hideTicks
                        orientation='bottom'
                        numTicks={isMobile ? 4 : 10}
                        axisLineClassName='stroke-[#ffffff]/10'
                        tickLength={4}
                        tickStroke='#FFFFFF4D'
                        tickFormat={(tick: number) =>
                          utc(tick).format('DD MMM')
                        }
                        tickComponent={({ x, y, formattedValue }) => (
                          <g>
                            <line
                              x1={x}
                              x2={x}
                              y1={y - 12.5}
                              y2={y - 8}
                              stroke='#FFFFFF4D'
                            />
                            <text
                              x={x}
                              y={y + 14}
                              fill='#909090'
                              fontSize={9}
                              fontWeight={300}
                              fontFamily='var(--font-sans)'
                              textAnchor='middle'
                            >
                              {formattedValue}
                            </text>
                          </g>
                        )}
                        tickLabelProps={() => ({
                          fill: '#909090',
                          fontSize: 9,
                          fontWeight: 300,
                          dy: 12,
                          fontFamily: 'var(--font-sans)',
                        })}
                      />
                    </Group>
                    <Group>
                      <Tooltip
                        verticalCrosshairStyle={{
                          stroke: '#FFFFFF99',
                          strokeDasharray: '4 4',
                        }}
                        snapTooltipToDatumX
                        snapTooltipToDatumY
                        showVerticalCrosshair
                        showSeriesGlyphs
                        renderTooltip={handleRenderTooltip}
                        renderGlyph={({ key }) => {
                          if (key === 'Buy Volume') {
                            return null;
                          }
                          return (
                            <circle
                              cx={0}
                              cy={0}
                              r={4}
                              fill={
                                key === 'Sell Volume' ? '#EB5347' : '#00DBBC'
                              }
                            />
                          );
                        }}
                      />
                    </Group>
                  </XYChart>
                  <rect
                    width={width}
                    height={height}
                    fill='transparent'
                    onTouchStart={zoom.dragStart}
                    onTouchMove={zoom.dragMove}
                    onTouchEnd={zoom.dragEnd}
                    onMouseDown={zoom.dragStart}
                    onMouseMove={zoom.dragMove}
                    onMouseUp={zoom.dragEnd}
                    onMouseLeave={() => {
                      if (zoom.isDragging) zoom.dragEnd();
                    }}
                    onDoubleClick={(event) => {
                      const point = localPoint(event) || { x: 0, y: 0 };
                      zoom.scale({
                        scaleX: transformMatrix.scaleX * 1.1,
                        scaleY: 1.1,
                        point,
                      });
                    }}
                    style={{
                      pointerEvents: zoom.isDragging ? 'all' : 'none',
                    }}
                  />
                </svg>
                <div className='flex flex-row items-center justify-end gap-2'>
                  <Button
                    size='icon'
                    className='border-[#494949]'
                    variant='secondary'
                    onClick={() => {
                      zoom.scale({
                        scaleX: 6 / 5,
                        scaleY: 1,
                      });
                    }}
                  >
                    <BiPlus size={16} />
                  </Button>
                  <Button
                    size='icon'
                    className='border-[#494949]'
                    variant='secondary'
                    onClick={() => {
                      zoom.scale({
                        scaleX: 5 / 6,
                        scaleY: 1,
                      });
                    }}
                  >
                    <BiMinus size={16} />
                  </Button>
                  <Button
                    variant='secondary'
                    className='h-fit py-1.5 text-xs font-medium hover:opacity-80'
                    onClick={zoom.reset}
                  >
                    Reset
                  </Button>
                </div>
              </div>
            );
          }}
        </Zoom>
      </div>
    </>
  );
});

Chart.displayName = 'Chart';

export const SubnetTotalPriceChart = () => {
  return <ParentSize>{({ width }) => <Chart width={width} />}</ParentSize>;
};
