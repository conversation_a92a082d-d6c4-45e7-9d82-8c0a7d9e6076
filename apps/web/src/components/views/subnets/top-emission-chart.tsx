'use client';

import { useMemo, useState } from 'react';
import { curveMonotoneX } from '@visx/curve';
import {
  AnimatedLineSeries,
  Axis,
  Grid,
  Tooltip,
  XYChart,
  buildChartTheme,
} from '@visx/xychart';
import { utc } from 'moment';
import { format } from 'numerable';
import { Skeleton, Text } from '@repo/ui/components';
import { cn, taoDivider, useWindowSize } from '@repo/ui/lib';
import { useTopSubnetEmission } from '@/lib/hooks';
import { useSubnetNames } from '@/lib/hooks/use-subnet-name';

const accessors = {
  xAccessor: (d: DataPoint) => new Date(d.x),
  yAccessor: (d: DataPoint) => d.y,
};

export type DataPoint = {
  x: string;
  y: number;
};

export const TopEmissionChart = () => {
  const { isMobile } = useWindowSize();
  const { data: subnetEmission, isPending } = useTopSubnetEmission();
  const { subnetMetadata } = useSubnetNames();
  const [selectLine, setSelectLine] = useState<string>('');
  const [disabledList, setDisabledList] = useState<string[]>([]);

  const topEmissions = useMemo(
    () =>
      subnetEmission?.flatMap((subnet) => {
        if (subnet.length > 0) {
          const subnetId = subnet[0].netuid;
          const subnetName =
            subnetMetadata.find((it) => it.netuid === subnetId)?.subnet_name ??
            'Unknown';

          return [
            {
              label: `${subnetId}: ${subnetName}`,
              data: subnet.map((item) => ({
                x: item.timestamp,
                y: Number(item.emission) / taoDivider,
              })),
            },
          ];
        }

        return [];
      }) ?? [],
    [subnetEmission, subnetMetadata]
  );

  const chartData = useMemo(
    () => topEmissions.filter((item) => !disabledList.includes(item.label)),
    [topEmissions, disabledList]
  );

  const customTheme = buildChartTheme({
    backgroundColor: '',
    colors: chartData.map((item) =>
      selectLine === item.label ? '#00DBBC' : '#3F3F3F'
    ),
    gridColor: '#e8e8e8',
    gridColorDark: '#222831',
    tickLength: 1,
  });

  return (
    <>
      {isPending ? (
        <Skeleton className='h-[600px]' />
      ) : (
        <>
          <div className='flex w-full flex-row items-start justify-between gap-10 xl:gap-20 2xl:gap-40'>
            <div className='grid w-full grid-cols-2 items-start justify-between gap-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6'>
              {topEmissions.map((item, index) => {
                const hue = (360 / topEmissions.length) * index;

                const saturation = 40;
                const lightness = 50;
                const color = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
                const transparentColor = `hsla(${hue}, ${saturation}%, ${lightness}%, 0.1)`;

                return (
                  <div
                    key={item.label}
                    style={{
                      backgroundColor: transparentColor,
                      border: `1px solid ${color}`,
                    }}
                    className={cn(
                      disabledList.includes(item.label) &&
                        'opacity-70 saturate-0',
                      'rounded-lg'
                    )}
                  >
                    <Text
                      key={item.label}
                      level='xs'
                      style={{ color }}
                      onMouseEnter={() => {
                        setSelectLine(item.label);
                      }}
                      onClick={() => {
                        setDisabledList((prev) =>
                          prev.includes(item.label)
                            ? prev.filter((it) => it !== item.label)
                            : disabledList.length !== 11
                              ? [...prev, item.label]
                              : [...prev]
                        );
                      }}
                      className={cn(
                        'flex cursor-pointer flex-col justify-between px-4 py-1 transition-all',
                        selectLine === item.label
                          ? 'brightness-200'
                          : 'opacity-100',
                        disabledList.includes(item.label)
                          ? 'line-through opacity-70 saturate-0'
                          : ''
                      )}
                    >
                      <span className='line-clamp-1 font-bold'>
                        {item.label}
                      </span>{' '}
                      <span className='font-thin'>
                        {format(item.data[0]?.y * 100, '0.00')}%
                      </span>
                    </Text>
                  </div>
                );
              })}
            </div>
          </div>
          <XYChart
            height={400}
            xScale={{ type: 'time' }}
            yScale={{ type: 'linear' }}
            margin={{ top: 20, right: 35, bottom: 40, left: 0 }}
            theme={customTheme}
          >
            <Grid
              rows={false}
              lineStyle={{
                stroke: '#FFFFFF',
                strokeOpacity: 0.2,
                strokeWidth: 1,
                strokeDasharray: '4 4',
              }}
            />
            <Grid
              columns={false}
              lineStyle={{
                stroke: '#FFFFFF',
                strokeOpacity: 0.2,
                strokeWidth: 1,
                strokeDasharray: '0',
              }}
            />
            <Axis
              tickLength={4}
              tickStroke='#FFFFFF33'
              axisLineClassName='stroke-[#ffffff]/10'
              orientation='right'
              tickFormat={(value) => `${(value * 100).toFixed(0)}%`}
              hideZero
              tickLabelProps={() => ({
                fill: '#757575',
                fontSize: 10,
                fontWeight: 300,
                fontFamily: 'var(--font-sans)',
                dx: '10px',
              })}
            />
            <Axis
              tickLength={4}
              tickStroke='#FFFFFF33'
              axisLineClassName='stroke-[#ffffff]/10'
              orientation='bottom'
              numTicks={isMobile ? 3 : 8}
              tickFormat={(date: string) => utc(date).format('MMM YY')}
              tickLabelProps={() => ({
                fill: '#7E7E7E',
                fontSize: 10,
                fontWeight: 300,
                fontFamily: 'var(--font-sans)',
                dy: '10px',
              })}
            />
            {chartData.map(({ label, data }) => {
              const newIndex = topEmissions.findIndex(
                (emission) => emission.label === label
              );
              const hue = (360 / topEmissions.length) * newIndex;

              const saturation = 40;
              const lightness = 50;
              const color = `hsl(${hue}, ${saturation}%, ${lightness}%)`;

              return (
                <AnimatedLineSeries
                  key={label}
                  dataKey={label}
                  data={data.slice().reverse()}
                  curve={curveMonotoneX}
                  {...accessors}
                  stroke={color}
                  opacity={selectLine === label ? 1 : 0.7}
                />
              );
            })}
            <Tooltip
              verticalCrosshairStyle={{
                stroke: '#FFFFFF99',
                strokeDasharray: '4 4',
              }}
              snapTooltipToDatumX
              snapTooltipToDatumY
              showVerticalCrosshair
              showSeriesGlyphs
              renderTooltip={({ tooltipData }) => {
                if (!tooltipData || !tooltipData.nearestDatum) {
                  return null;
                }

                const { key } = tooltipData.nearestDatum;
                setSelectLine(key);

                return (
                  <div
                    key={key}
                    className='z-10 flex flex-col gap-2 rounded-lg border border-[#323232] bg-[#1D1D1D]/60 p-3 backdrop-blur-xl'
                  >
                    {chartData.map((item) => {
                      const datum = tooltipData.datumByKey[item.label]
                        .datum as DataPoint;

                      const newIndex = topEmissions.findIndex(
                        (emission) => emission.label === item.label
                      );
                      const hue = (360 / topEmissions.length) * newIndex;
                      const saturation = 40;
                      const lightness = 50;
                      const color = `hsl(${hue}, ${saturation}%, ${lightness}%)`;

                      return (
                        <div
                          className='flex flex-row items-center justify-between gap-6'
                          key={item.label}
                        >
                          <Text
                            level='xs'
                            className={cn(
                              'font-light',
                              key === item.label ? 'opacity-100' : 'opacity-50'
                            )}
                          >
                            {item.label}
                          </Text>
                          <Text
                            style={{ color }}
                            level='xs'
                            className='font-medium brightness-125'
                          >
                            {format(datum.y * 100, '0.00')}%
                          </Text>
                        </div>
                      );
                    })}
                  </div>
                );
              }}
            />
          </XYChart>
        </>
      )}
    </>
  );
};
